// 从文本分析导入数据到Neo4j

// 1. 清空现有数据（可选）
// MATCH (n) DETACH DELETE n;

// 2. 导入人员数据
LOAD CSV WITH HEADERS FROM 'file:///people_from_text.csv' AS row
CREATE (p:Person {
  name: row.name,
  job: row.job,
  location: row.location,
  skills: split(row.skills, ';'),
  source: 'text_analysis'
});

// 3. 导入关系数据
LOAD CSV WITH HEADERS FROM 'file:///relationships_from_text.csv' AS row
MATCH (p1:Person {name: row.person1})
MATCH (p2:Person {name: row.person2})
CREATE (p1)-[r:RELATIONSHIP {
  type: row.relationship_type,
  context: row.context,
  source: 'text_analysis'
}]->(p2);

// 4. 导入活动数据并创建参与关系
LOAD CSV WITH HEADERS FROM 'file:///activities_from_text.csv' AS row
CREATE (a:Activity {
  name: row.activity_name,
  frequency: row.frequency,
  source: 'text_analysis'
})
WITH a, row
UNWIND split(row.participants, ';') AS participant_name
MATCH (p:Person {name: participant_name})
CREATE (p)-[:PARTICIPATES_IN]->(a);

// 5. 创建基于共同活动的关系
MATCH (p1:Person)-[:PARTICIPATES_IN]->(a:Activity)<-[:PARTICIPATES_IN]-(p2:Person)
WHERE p1.name <> p2.name
CREATE (p1)-[:SHARES_ACTIVITY {activity: a.name}]->(p2);

// 6. 创建基于相同技能的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.name <> p2.name
AND any(skill IN p1.skills WHERE skill IN p2.skills)
WITH p1, p2, [skill IN p1.skills WHERE skill IN p2.skills] as shared_skills
CREATE (p1)-[:SHARES_SKILLS {skills: shared_skills}]->(p2);

// 7. 创建基于相同工作地点的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.location = p2.location AND p1.name <> p2.name
CREATE (p1)-[:SAME_LOCATION]->(p2);

// 8. 查看导入结果
MATCH (n) RETURN labels(n) as 节点类型, count(n) as 数量;

// 9. 查看关系统计
MATCH ()-[r]->() RETURN type(r) as 关系类型, count(r) as 数量;

// 10. 可视化整个图
MATCH (n)-[r]->(m) RETURN n, r, m LIMIT 50;