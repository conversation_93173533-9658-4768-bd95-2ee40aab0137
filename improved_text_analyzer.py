#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的文本分析工具 - 手动解析示例
"""

def manual_text_analysis():
    """手动分析示例文本，展示如何将非结构化文本转换为结构化数据"""
    
    # 原始文本
    text = """张三是一名软件工程师，他在北京工作。张三的同事李四也是程序员，他们经常一起讨论技术问题。
李四来自上海，但现在在北京的同一家公司工作。王五是他们的项目经理，负责协调整个团队。
张三和李四都喜欢编程，而王五更擅长项目管理。他们三个人经常在周末一起踢足球。
公司里还有一个设计师赵六，她负责用户界面设计。赵六和张三合作过很多项目。
最近，公司招聘了一名新的数据分析师钱七，她专门负责数据挖掘和分析工作。"""
    
    print("📄 原始文本:")
    print(text)
    print("\n" + "="*60 + "\n")
    
    # 手动提取的结构化信息
    people = [
        {"name": "张三", "job": "软件工程师", "location": "北京", "skills": ["编程", "技术讨论"]},
        {"name": "李四", "job": "程序员", "location": "北京", "origin": "上海", "skills": ["编程", "技术讨论"]},
        {"name": "王五", "job": "项目经理", "location": "北京", "skills": ["项目管理", "团队协调"]},
        {"name": "赵六", "job": "设计师", "location": "北京", "skills": ["用户界面设计"]},
        {"name": "钱七", "job": "数据分析师", "location": "北京", "skills": ["数据挖掘", "数据分析"]}
    ]
    
    relationships = [
        {"person1": "张三", "person2": "李四", "type": "同事", "context": "一起讨论技术问题"},
        {"person1": "张三", "person2": "王五", "type": "下属", "context": "王五是项目经理"},
        {"person1": "李四", "person2": "王五", "type": "下属", "context": "王五是项目经理"},
        {"person1": "张三", "person2": "李四", "type": "朋友", "context": "都喜欢编程"},
        {"person1": "张三", "person2": "王五", "type": "朋友", "context": "一起踢足球"},
        {"person1": "李四", "person2": "王五", "type": "朋友", "context": "一起踢足球"},
        {"person1": "张三", "person2": "赵六", "type": "合作伙伴", "context": "合作过很多项目"}
    ]
    
    activities = [
        {"activity": "踢足球", "participants": ["张三", "李四", "王五"], "frequency": "周末"},
        {"activity": "技术讨论", "participants": ["张三", "李四"], "frequency": "经常"},
        {"activity": "项目合作", "participants": ["张三", "赵六"], "frequency": "很多项目"}
    ]
    
    print("👥 提取的人员信息:")
    for person in people:
        print(f"  • {person['name']}: {person['job']} | {person['location']} | 技能: {', '.join(person['skills'])}")
    
    print(f"\n🔗 提取的关系信息:")
    for rel in relationships:
        print(f"  • {rel['person1']} --[{rel['type']}]--> {rel['person2']} ({rel['context']})")
    
    print(f"\n🎯 提取的活动信息:")
    for activity in activities:
        print(f"  • {activity['activity']}: {', '.join(activity['participants'])} ({activity['frequency']})")
    
    return people, relationships, activities

def create_csv_files(people, relationships, activities):
    """创建CSV文件"""
    import csv
    
    base_path = "neo4j-community-2025.08.0-windows/neo4j-community-2025.08.0/import"
    
    # 人员CSV
    with open(f'{base_path}/people_from_text.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['name', 'job', 'location', 'skills'])
        writer.writeheader()
        for person in people:
            writer.writerow({
                'name': person['name'],
                'job': person['job'],
                'location': person['location'],
                'skills': ';'.join(person['skills'])
            })
    
    # 关系CSV
    with open(f'{base_path}/relationships_from_text.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['person1', 'person2', 'relationship_type', 'context'])
        writer.writeheader()
        for rel in relationships:
            writer.writerow({
                'person1': rel['person1'],
                'person2': rel['person2'],
                'relationship_type': rel['type'],
                'context': rel['context']
            })
    
    # 活动CSV
    with open(f'{base_path}/activities_from_text.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['activity_name', 'participants', 'frequency'])
        writer.writeheader()
        for activity in activities:
            writer.writerow({
                'activity_name': activity['activity'],
                'participants': ';'.join(activity['participants']),
                'frequency': activity['frequency']
            })
    
    print(f"\n✅ 已创建CSV文件:")
    print(f"  • {base_path}/people_from_text.csv")
    print(f"  • {base_path}/relationships_from_text.csv")
    print(f"  • {base_path}/activities_from_text.csv")

def create_cypher_script():
    """创建Neo4j导入脚本"""
    
    cypher_script = '''// 从文本分析导入数据到Neo4j

// 1. 清空现有数据（可选）
// MATCH (n) DETACH DELETE n;

// 2. 导入人员数据
LOAD CSV WITH HEADERS FROM 'file:///people_from_text.csv' AS row
CREATE (p:Person {
  name: row.name,
  job: row.job,
  location: row.location,
  skills: split(row.skills, ';'),
  source: 'text_analysis'
});

// 3. 导入关系数据
LOAD CSV WITH HEADERS FROM 'file:///relationships_from_text.csv' AS row
MATCH (p1:Person {name: row.person1})
MATCH (p2:Person {name: row.person2})
CREATE (p1)-[r:RELATIONSHIP {
  type: row.relationship_type,
  context: row.context,
  source: 'text_analysis'
}]->(p2);

// 4. 导入活动数据并创建参与关系
LOAD CSV WITH HEADERS FROM 'file:///activities_from_text.csv' AS row
CREATE (a:Activity {
  name: row.activity_name,
  frequency: row.frequency,
  source: 'text_analysis'
})
WITH a, row
UNWIND split(row.participants, ';') AS participant_name
MATCH (p:Person {name: participant_name})
CREATE (p)-[:PARTICIPATES_IN]->(a);

// 5. 创建基于共同活动的关系
MATCH (p1:Person)-[:PARTICIPATES_IN]->(a:Activity)<-[:PARTICIPATES_IN]-(p2:Person)
WHERE p1.name <> p2.name
CREATE (p1)-[:SHARES_ACTIVITY {activity: a.name}]->(p2);

// 6. 创建基于相同技能的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.name <> p2.name
AND any(skill IN p1.skills WHERE skill IN p2.skills)
WITH p1, p2, [skill IN p1.skills WHERE skill IN p2.skills] as shared_skills
CREATE (p1)-[:SHARES_SKILLS {skills: shared_skills}]->(p2);

// 7. 创建基于相同工作地点的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.location = p2.location AND p1.name <> p2.name
CREATE (p1)-[:SAME_LOCATION]->(p2);

// 8. 查看导入结果
MATCH (n) RETURN labels(n) as 节点类型, count(n) as 数量;

// 9. 查看关系统计
MATCH ()-[r]->() RETURN type(r) as 关系类型, count(r) as 数量;

// 10. 可视化整个图
MATCH (n)-[r]->(m) RETURN n, r, m LIMIT 50;'''
    
    with open('neo4j-community-2025.08.0-windows/neo4j-community-2025.08.0/import/text_analysis_import.cypher', 'w', encoding='utf-8') as f:
        f.write(cypher_script)
    
    print(f"  • neo4j-community-2025.08.0-windows/neo4j-community-2025.08.0/import/text_analysis_import.cypher")

def main():
    print("🔄 将非结构化文本转换为图数据库")
    print("="*60)
    
    # 分析文本
    people, relationships, activities = manual_text_analysis()
    
    # 创建CSV文件
    create_csv_files(people, relationships, activities)
    
    # 创建Cypher脚本
    create_cypher_script()
    
    print(f"\n🚀 下一步操作:")
    print(f"1. 在Neo4j Web界面 (http://localhost:7474/) 中")
    print(f"2. 复制并运行 text_analysis_import.cypher 中的查询")
    print(f"3. 查看生成的图数据库！")
    
    print(f"\n💡 这个例子展示了如何:")
    print(f"  ✓ 将非结构化文本转换为结构化数据")
    print(f"  ✓ 提取实体（人、活动）和关系")
    print(f"  ✓ 创建可导入Neo4j的CSV文件")
    print(f"  ✓ 自动推断隐含关系")

if __name__ == "__main__":
    main()
