
// 从文本分析结果导入数据

// 1. 导入人员
LOAD CSV WITH HEADERS FROM 'file:///extracted_people.csv' AS row
CREATE (p:Person {
  name: row.name,
  job: CASE WHEN row.job = '' THEN null ELSE row.job END,
  location: CASE WHEN row.location = '' THEN null ELSE row.location END,
  skills: CASE WHEN row.skills = '' THEN [] ELSE split(row.skills, ';') END,
  source: 'text_extraction'
});

// 2. 导入关系
LOAD CSV WITH HEADERS FROM 'file:///extracted_relationships.csv' AS row
MATCH (p1:Person {name: row.person1})
MATCH (p2:Person {name: row.person2})
CREATE (p1)-[r:RELATIONSHIP {
  type: row.relationship_type,
  source: row.source
}]->(p2);

// 3. 创建基于职业的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.job = p2.job AND p1.name <> p2.name
CREATE (p1)-[:SAME_JOB]->(p2);

// 4. 创建基于地点的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.location = p2.location AND p1.name <> p2.name
CREATE (p1)-[:SAME_LOCATION]->(p2);

// 5. 创建基于技能的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.name <> p2.name
AND any(skill IN p1.skills WHERE skill IN p2.skills)
CREATE (p1)-[:SHARED_SKILLS]->(p2);

// 6. 查看结果
MATCH (n)-[r]->(m) RETURN n, r, m;
