#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本转图数据库工具
将非结构化文本转换为Neo4j可导入的CSV格式
"""

import re
import csv
import json

def analyze_text(text):
    """分析文本，提取人物、职业、地点、关系等信息"""
    
    # 定义实体和关系模式
    people = []
    relationships = []
    locations = []
    jobs = []
    
    # 简单的实体识别（实际项目中可以使用NLP库如spaCy）
    
    # 提取人名（中文姓名模式）
    name_pattern = r'[张李王赵钱孙周吴郑][一二三四五六七八九十\w]+'
    names = re.findall(name_pattern, text)
    names = list(set(names))  # 去重
    
    # 提取职业
    job_patterns = {
        '软件工程师': ['软件工程师', '程序员'],
        '项目经理': ['项目经理'],
        '设计师': ['设计师'],
        '数据分析师': ['数据分析师']
    }
    
    # 提取地点
    location_pattern = r'[北京|上海|深圳|广州]+'
    locations = re.findall(location_pattern, text)
    locations = list(set(locations))
    
    # 分析每个人的信息
    person_info = {}
    for name in names:
        person_info[name] = {
            'name': name,
            'job': None,
            'location': None,
            'skills': [],
            'relationships': []
        }
        
        # 查找职业信息
        for job_title, keywords in job_patterns.items():
            for keyword in keywords:
                if f"{name}是一名{keyword}" in text or f"{name}也是{keyword}" in text:
                    person_info[name]['job'] = job_title
                    break
        
        # 查找地点信息
        for location in locations:
            if f"{name}" in text and location in text:
                # 简单的共现分析
                name_pos = text.find(name)
                location_pos = text.find(location)
                if abs(name_pos - location_pos) < 50:  # 如果在50个字符内出现
                    person_info[name]['location'] = location
        
        # 查找技能
        if '编程' in text and name in text:
            if f"{name}" in text and '编程' in text:
                person_info[name]['skills'].append('编程')
        
        if '项目管理' in text and name in text:
            if f"{name}" in text and '项目管理' in text:
                person_info[name]['skills'].append('项目管理')
    
    # 分析关系
    relationship_patterns = [
        (r'(\w+)的同事(\w+)', '同事'),
        (r'(\w+)和(\w+)都喜欢', '共同爱好'),
        (r'(\w+)和(\w+)合作过', '合作'),
        (r'(\w+)、(\w+).*一起', '朋友'),
    ]
    
    relationships = []
    for pattern, rel_type in relationship_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if len(match) == 2:
                person1, person2 = match
                if person1 in names and person2 in names:
                    relationships.append({
                        'person1': person1,
                        'person2': person2,
                        'relationship': rel_type,
                        'source': 'text_analysis'
                    })
    
    return person_info, relationships

def save_to_csv(person_info, relationships, output_dir):
    """将分析结果保存为CSV文件"""
    
    # 保存人员信息
    with open(f'{output_dir}/extracted_people.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['name', 'job', 'location', 'skills'])
        
        for name, info in person_info.items():
            skills_str = ';'.join(info['skills']) if info['skills'] else ''
            writer.writerow([
                info['name'],
                info['job'] or '',
                info['location'] or '',
                skills_str
            ])
    
    # 保存关系信息
    with open(f'{output_dir}/extracted_relationships.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['person1', 'person2', 'relationship_type', 'source'])
        
        for rel in relationships:
            writer.writerow([
                rel['person1'],
                rel['person2'],
                rel['relationship'],
                rel['source']
            ])

def main():
    # 读取文本文件
    with open('neo4j-community-2025.08.0-windows/neo4j-community-2025.08.0/import/sample_text.txt', 'r', encoding='utf-8') as f:
        text = f.read()
    
    print("原始文本:")
    print(text)
    print("\n" + "="*50 + "\n")
    
    # 分析文本
    person_info, relationships = analyze_text(text)
    
    print("提取的人员信息:")
    for name, info in person_info.items():
        print(f"- {name}: {info}")
    
    print(f"\n提取的关系信息:")
    for rel in relationships:
        print(f"- {rel['person1']} --[{rel['relationship']}]--> {rel['person2']}")
    
    # 保存为CSV
    output_dir = 'neo4j-community-2025.08.0-windows/neo4j-community-2025.08.0/import'
    save_to_csv(person_info, relationships, output_dir)
    
    print(f"\n✅ 已生成CSV文件:")
    print(f"- {output_dir}/extracted_people.csv")
    print(f"- {output_dir}/extracted_relationships.csv")
    
    # 生成Neo4j导入脚本
    cypher_script = """
// 从文本分析结果导入数据

// 1. 导入人员
LOAD CSV WITH HEADERS FROM 'file:///extracted_people.csv' AS row
CREATE (p:Person {
  name: row.name,
  job: CASE WHEN row.job = '' THEN null ELSE row.job END,
  location: CASE WHEN row.location = '' THEN null ELSE row.location END,
  skills: CASE WHEN row.skills = '' THEN [] ELSE split(row.skills, ';') END,
  source: 'text_extraction'
});

// 2. 导入关系
LOAD CSV WITH HEADERS FROM 'file:///extracted_relationships.csv' AS row
MATCH (p1:Person {name: row.person1})
MATCH (p2:Person {name: row.person2})
CREATE (p1)-[r:RELATIONSHIP {
  type: row.relationship_type,
  source: row.source
}]->(p2);

// 3. 创建基于职业的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.job = p2.job AND p1.name <> p2.name
CREATE (p1)-[:SAME_JOB]->(p2);

// 4. 创建基于地点的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.location = p2.location AND p1.name <> p2.name
CREATE (p1)-[:SAME_LOCATION]->(p2);

// 5. 创建基于技能的关系
MATCH (p1:Person), (p2:Person)
WHERE p1.name <> p2.name
AND any(skill IN p1.skills WHERE skill IN p2.skills)
CREATE (p1)-[:SHARED_SKILLS]->(p2);

// 6. 查看结果
MATCH (n)-[r]->(m) RETURN n, r, m;
"""
    
    with open(f'{output_dir}/import_from_text.cypher', 'w', encoding='utf-8') as f:
        f.write(cypher_script)
    
    print(f"- {output_dir}/import_from_text.cypher")
    print("\n🚀 现在可以在Neo4j中运行这些Cypher查询来导入数据！")

if __name__ == "__main__":
    main()
